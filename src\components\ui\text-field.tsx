import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

const textFieldVariants = cva(
  "relative flex items-center",
  {
    variants: {
      variant: {
        default: "",
        filled: "bg-muted rounded-md",
        outline: "border border-input rounded-md",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

interface TextFieldProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof textFieldVariants> {
  label?: string
  helpText?: string
  icon?: React.ReactNode
  children: React.ReactNode
}

function TextField({
  className,
  variant,
  label,
  helpText,
  icon,
  children,
  ...props
}: TextFieldProps) {
  return (
    <div className="flex flex-col gap-1">
      {label && (
        <label className="text-sm font-medium text-foreground">
          {label}
        </label>
      )}
      <div
        data-slot="text-field"
        className={cn(textFieldVariants({ variant }), className)}
        {...props}
      >
        {icon && (
          <div className="absolute left-3 z-10 text-muted-foreground [&_svg]:size-4">
            {icon}
          </div>
        )}
        <div className={cn("w-full", icon && "pl-10")}>
          {children}
        </div>
      </div>
      {helpText && (
        <p className="text-sm text-muted-foreground">
          {helpText}
        </p>
      )}
    </div>
  )
}

function TextFieldInput({
  className,
  ...props
}: React.ComponentProps<typeof Input>) {
  return (
    <Input
      data-slot="text-field-input"
      className={cn("border-0 bg-transparent shadow-none focus-visible:ring-0", className)}
      {...props}
    />
  )
}

// Add compound component pattern
TextField.Input = TextFieldInput

export { TextField, TextFieldInput, textFieldVariants }
