import * as React from "react"
import { cn } from "@/lib/utils"

interface DefaultPageLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

function DefaultPageLayout({
  className,
  children,
  ...props
}: DefaultPageLayoutProps) {
  return (
    <div
      data-slot="default-page-layout"
      className={cn("min-h-screen bg-background text-foreground", className)}
      {...props}
    >
      {children}
    </div>
  )
}

export { DefaultPageLayout }
