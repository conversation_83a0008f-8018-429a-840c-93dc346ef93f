import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const iconWithBackgroundVariants = cva(
  "inline-flex items-center justify-center rounded-md shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground",
        neutral: "bg-muted text-muted-foreground",
        success: "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400",
        warning: "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400",
        destructive: "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400",
      },
      size: {
        small: "size-6 [&_svg]:size-3",
        medium: "size-8 [&_svg]:size-4",
        large: "size-10 [&_svg]:size-5",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "medium",
    },
  }
)

interface IconWithBackgroundProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconWithBackgroundVariants> {
  icon: React.ReactNode
}

function IconWithBackground({
  className,
  variant,
  size,
  icon,
  ...props
}: IconWithBackgroundProps) {
  return (
    <div
      data-slot="icon-with-background"
      className={cn(iconWithBackgroundVariants({ variant, size }), className)}
      {...props}
    >
      {icon}
    </div>
  )
}

export { IconWithBackground, iconWithBackgroundVariants }
