import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { Button, buttonVariants } from "@/components/ui/button"

const iconButtonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90",
        outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        "neutral-primary": "bg-muted text-muted-foreground hover:bg-muted/80",
        "brand-tertiary": "bg-primary/10 text-primary hover:bg-primary/20",
      },
      size: {
        default: "size-9 [&_svg]:size-4",
        sm: "size-8 [&_svg]:size-3.5",
        lg: "size-10 [&_svg]:size-5",
        small: "size-8 [&_svg]:size-3.5", // alias for sm to match original API
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

interface IconButtonProps
  extends React.ComponentProps<"button">,
    VariantProps<typeof iconButtonVariants> {
  icon: React.ReactNode
  asChild?: boolean
}

function IconButton({
  className,
  variant,
  size,
  icon,
  asChild = false,
  ...props
}: IconButtonProps) {
  return (
    <Button
      data-slot="icon-button"
      className={cn(iconButtonVariants({ variant, size }), className)}
      asChild={asChild}
      {...props}
    >
      {icon}
    </Button>
  )
}

export { IconButton, iconButtonVariants }
