"use client";

import React from "react";
import { DefaultPageLayout } from "@/components/layouts/default-page-layout";
import { TextField } from "@/components/ui/text-field";
import { Search } from "lucide-react";
import { IconButton } from "@/components/ui/icon-button";
import { Bell } from "lucide-react";
import { HelpCircle } from "lucide-react";
import { Grip } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { IconWithBackground } from "@/components/ui/icon-with-background";
import { LineChart } from "lucide-react";
import { ArrowLeft } from "lucide-react";
import { ChevronDown } from "lucide-react";
import { ArrowDownRight } from "lucide-react";
import { Star } from "lucide-react";
import { GripVertical } from "lucide-react";
import { ArrowUpRight } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { TrendingUp } from "lucide-react";
import { Zap } from "lucide-react";
import { ToggleGroup } from "@/components/ui/toggle-group";
import { ArrowUpDown } from "lucide-react";
import { DollarSign } from "lucide-react";
import { ChevronRight } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Send } from "lucide-react";
import { Download } from "lucide-react";
import { CreditCard } from "lucide-react";
import { Button } from "@/components/ui/button";

function CryptoDashboard() {
  return (
    <DefaultPageLayout>
      <div className="flex h-full w-full flex-col items-start bg-default-background">
        <div className="flex w-full items-center gap-4 border-b border-solid border-neutral-border px-6 py-6">
          <span className="grow shrink-0 basis-0 text-heading-2 font-heading-2 text-default-font">
            Home
          </span>
          <div className="flex items-start gap-2">
            <TextField
              variant="filled"
              label=""
              helpText=""
              icon={<Search />}
            >
              <TextField.Input
                placeholder="Search"
                value=""
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {}}
              />
            </TextField>
            <IconButton
              variant="neutral-primary"
              icon={<Bell />}
              onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
            />
            <IconButton
              variant="neutral-primary"
              icon={<HelpCircle />}
              onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
            />
            <IconButton
              variant="neutral-primary"
              icon={<Grip />}
              onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
            />
            <Avatar>
              <AvatarImage src="https://res.cloudinary.com/subframe/image/upload/v1711417507/shared/fychrij7dzl8wgq2zjq9.avif" />
              <AvatarFallback>A</AvatarFallback>
            </Avatar>
          </div>
        </div>
        <div className="flex w-full grow shrink-0 basis-0 flex-wrap items-start">
          <div className="flex grow shrink-0 basis-0 flex-col items-start self-stretch overflow-auto">
            <div className="flex w-full flex-col items-start gap-6 border-b border-solid border-neutral-border px-6 py-6">
              <span className="w-full text-heading-1 font-heading-1 text-default-font">
                $47,031.80
              </span>
              <div className="flex w-full flex-col items-start gap-6">
                <div className="flex w-full flex-wrap items-center gap-2">
                  <div className="flex grow shrink-0 basis-0 items-center gap-2">
                    <IconWithBackground
                      variant="neutral"
                      size="medium"
                      icon={<LineChart />}
                    />
                    <span className="text-body-bold font-body-bold text-default-font">
                      Crypto
                    </span>
                  </div>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Balance
                    </span>
                    <span className="text-body font-body text-default-font">
                      $47,031.80
                    </span>
                  </div>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Staking
                    </span>
                    <span className="text-body font-body text-success-600">
                      Earn up to 2.45% APY
                    </span>
                  </div>
                </div>
                <div className="flex w-full flex-wrap items-center gap-2">
                  <div className="flex grow shrink-0 basis-0 items-center gap-2">
                    <IconWithBackground
                      variant="neutral"
                      size="medium"
                      icon={<LineChart />}
                    />
                    <span className="text-body-bold font-body-bold text-default-font">
                      Cash
                    </span>
                  </div>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Balance
                    </span>
                    <span className="text-body font-body text-default-font">
                      $47,031.80
                    </span>
                  </div>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Earn
                    </span>
                    <span className="text-body font-body text-success-600">
                      Earn up to 2.45% APY
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-full flex-col items-start gap-6 border-b border-solid border-neutral-border px-6 py-6">
              <div className="flex w-full items-center gap-2">
                <span className="grow shrink-0 basis-0 text-heading-3 font-heading-3 text-default-font">
                  For you
                </span>
                <div className="flex items-start gap-2">
                  <IconButton
                    icon={<ArrowLeft />}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  />
                  <IconButton
                    icon={<ChevronRight />}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  />
                </div>
              </div>
              <div className="flex w-full flex-wrap items-start gap-4">
                <div className="flex items-start gap-6 self-stretch rounded-md bg-neutral-100 px-4 py-4">
                  <img
                    className="h-12 w-12 flex-none rounded-sm object-cover"
                    src="https://res.cloudinary.com/subframe/image/upload/v1724705499/uploads/302/jh06ubduyciizexxi4ep.png"
                  />
                  <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 self-stretch">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Learn to trade
                    </span>
                    <span className="text-caption font-caption text-subtext-color">
                      Fees as low as 0.00% for a limited time
                    </span>
                  </div>
                </div>
                <div className="flex items-start gap-6 self-stretch rounded-md bg-neutral-100 px-4 py-4">
                  <img
                    className="h-12 w-12 flex-none rounded-sm object-cover"
                    src="https://res.cloudinary.com/subframe/image/upload/v1723780878/uploads/302/mdjcme9tm4svgmkjv4zf.png"
                  />
                  <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 self-stretch">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Earn rewards
                    </span>
                    <span className="text-caption font-caption text-subtext-color">
                      Stake your crypto assets to earn
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-full flex-col items-start gap-6 border-b border-solid border-neutral-border px-6 py-6">
              <div className="flex w-full items-center gap-2">
                <span className="grow shrink-0 basis-0 text-heading-3 font-heading-3 text-default-font">
                  Prices
                </span>
                <Button
                  variant="outline"
                  onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                >
                  Watchlist
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="flex w-full flex-col items-start gap-6">
                <div className="flex w-full items-center gap-4">
                  <Avatar>
                    <AvatarImage src="https://res.cloudinary.com/subframe/image/upload/v1723780835/uploads/302/kr9usrdgbwp9cge3ab1f.png" />
                    <AvatarFallback>B</AvatarFallback>
                  </Avatar>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Ethereum
                    </span>
                    <span className="text-body font-body text-subtext-color">
                      ETH
                    </span>
                  </div>
                  <span className="grow shrink-0 basis-0 text-body font-body text-default-font">
                    $3,456.78
                  </span>
                  <div className="flex grow shrink-0 basis-0 items-center gap-1">
                    <ArrowDownRight className="text-body font-body text-error-700" />
                    <span className="text-body font-body text-error-700">
                      2.34%
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  >
                    Buy
                  </Button>
                  <IconButton
                    variant="outline"
                    size="small"
                    icon={<Star />}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  />
                  <GripVertical className="text-body font-body text-neutral-400" />
                </div>
                <div className="flex w-full items-center gap-4">
                  <Avatar>
                    <AvatarImage src="https://res.cloudinary.com/subframe/image/upload/v1723780878/uploads/302/mdjcme9tm4svgmkjv4zf.png" />
                    <AvatarFallback>D</AvatarFallback>
                  </Avatar>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Dogecoin
                    </span>
                    <span className="text-body font-body text-subtext-color">
                      DOGE
                    </span>
                  </div>
                  <span className="grow shrink-0 basis-0 text-body font-body text-default-font">
                    $0.22
                  </span>
                  <div className="flex grow shrink-0 basis-0 items-center gap-1">
                    <ArrowUpRight className="text-body font-body text-success-600" />
                    <span className="text-body font-body text-success-600">
                      1.45%
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  >
                    Buy
                  </Button>
                  <IconButton
                    variant="outline"
                    size="small"
                    icon={<Star />}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  />
                  <GripVertical className="text-body font-body text-neutral-400" />
                </div>
                <div className="flex w-full items-center gap-4">
                  <Avatar>
                    <AvatarImage src="https://res.cloudinary.com/subframe/image/upload/v1723780719/uploads/302/lf4i2zybfw9xxl56w6ce.png" />
                    <AvatarFallback>S</AvatarFallback>
                  </Avatar>
                  <div className="flex grow shrink-0 basis-0 flex-col items-start">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Solana
                    </span>
                    <span className="text-body font-body text-subtext-color">
                      SOL
                    </span>
                  </div>
                  <span className="grow shrink-0 basis-0 text-body font-body text-default-font">
                    $180.45
                  </span>
                  <div className="flex grow shrink-0 basis-0 items-center gap-1">
                    <ArrowUpRight className="text-body font-body text-success-600" />
                    <span className="text-body font-body text-success-600">
                      +3.21%
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  >
                    Buy
                  </Button>
                  <IconButton
                    variant="outline"
                    size="small"
                    icon={<Star />}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  />
                  <GripVertical className="text-body font-body text-neutral-400" />
                </div>
              </div>
            </div>
            <div className="flex w-full flex-col items-start gap-6 border-b border-solid border-neutral-border px-6 py-6">
              <div className="flex w-full items-center gap-2">
                <span className="grow shrink-0 basis-0 text-heading-3 font-heading-3 text-default-font">
                  Market Overview
                </span>
                <Button
                  variant="outline"
                  onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                >
                  24h
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="flex w-full flex-wrap items-start gap-4">
                <div className="flex grow shrink-0 basis-0 flex-col items-start gap-2 rounded-md bg-neutral-100 px-6 py-6">
                  <span className="text-caption font-caption text-subtext-color">
                    Trading Volume
                  </span>
                  <span className="text-heading-2 font-heading-2 text-default-font">
                    $24.8B
                  </span>
                  <div className="flex items-center gap-1">
                    <ArrowUpRight className="text-body font-body text-success-600" />
                    <span className="text-body font-body text-success-600">
                      12.4%
                    </span>
                  </div>
                </div>
                <div className="flex grow shrink-0 basis-0 flex-col items-start gap-2 rounded-md bg-neutral-100 px-6 py-6">
                  <span className="text-caption font-caption text-subtext-color">
                    Active Traders
                  </span>
                  <span className="text-heading-2 font-heading-2 text-default-font">
                    234.5K
                  </span>
                  <div className="flex items-center gap-1">
                    <ArrowUpRight className="text-body font-body text-success-600" />
                    <span className="text-body font-body text-success-600">
                      5.2%
                    </span>
                  </div>
                </div>
                <div className="flex grow shrink-0 basis-0 flex-col items-start gap-2 rounded-md bg-neutral-100 px-6 py-6">
                  <span className="text-caption font-caption text-subtext-color">
                    Market Sentiment
                  </span>
                  <span className="text-heading-2 font-heading-2 text-default-font">
                    Bullish
                  </span>
                  <div className="flex h-5 w-full flex-none flex-col items-center justify-center gap-2">
                    <Progress value={75} />
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-full flex-col items-start gap-6 px-6 py-6">
              <div className="flex w-full items-center gap-2">
                <span className="grow shrink-0 basis-0 text-heading-3 font-heading-3 text-default-font">
                  Market News
                </span>
                <Button
                  variant="outline"
                  onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                >
                  All Categories
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="flex w-full flex-col items-start">
                <div className="flex w-full flex-wrap items-start gap-6 rounded-md px-3 py-4">
                  <div className="flex items-center gap-6">
                    <IconWithBackground
                      size="medium"
                      icon={<TrendingUp />}
                    />
                  </div>
                  <div className="flex grow shrink-0 basis-0 flex-wrap items-start gap-6">
                    <div className="flex min-w-[192px] grow shrink-0 basis-0 flex-col items-start gap-1">
                      <span className="w-full text-heading-3 font-heading-3 text-default-font">
                        Bitcoin Reaches New ATH
                      </span>
                      <span className="w-full text-body font-body text-default-font">
                        Bitcoin surpasses previous records amid institutional
                        adoption
                      </span>
                    </div>
                    <span className="text-body font-body text-subtext-color">
                      5m ago
                    </span>
                  </div>
                </div>
                <div className="flex w-full flex-wrap items-start gap-6 rounded-md px-3 py-4">
                  <div className="flex items-center gap-6">
                    <IconWithBackground
                      variant="warning"
                      size="medium"
                      icon={<Zap />}
                    />
                  </div>
                  <div className="flex grow shrink-0 basis-0 flex-wrap items-start gap-6">
                    <div className="flex min-w-[192px] grow shrink-0 basis-0 flex-col items-start gap-1">
                      <span className="w-full text-heading-3 font-heading-3 text-default-font">
                        ETH 2.0 Update
                      </span>
                      <span className="w-full text-body font-body text-default-font">
                        Ethereum network prepares for major protocol upgrade
                      </span>
                    </div>
                    <span className="text-body font-body text-subtext-color">
                      15m ago
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex w-96 flex-none flex-col items-start self-stretch border-l border-solid border-neutral-border">
            <div className="flex w-full flex-col items-start gap-6 border-b border-solid border-neutral-border px-6 py-6">
              <ToggleGroup type="single" value="" onValueChange={(value: string) => {}}>
                <ToggleGroup.Item value="9d6ba89d">
                  Buy
                </ToggleGroup.Item>
                <ToggleGroup.Item value="3644d9eb">
                  Sell
                </ToggleGroup.Item>
                <ToggleGroup.Item value="56d6e41e">
                  Convert
                </ToggleGroup.Item>
              </ToggleGroup>
              <Button
                variant="outline"
                onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
              >
                One-time order
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
              <div className="flex w-full flex-col items-start gap-2">
                <div className="flex w-full items-center gap-2">
                  <div className="flex grow shrink-0 basis-0 items-center gap-1">
                    <span className="text-heading-1 font-heading-1 text-default-font">
                      0
                    </span>
                    <span className="text-heading-1 font-heading-1 text-neutral-400">
                      USD
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  >
                    Max
                  </Button>
                </div>
                <div className="flex items-center gap-1">
                  <IconButton
                    variant="outline"
                    size="small"
                    icon={<ArrowUpDown />}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
                  />
                  <span className="text-body font-body text-brand-700">
                    0 BTC
                  </span>
                </div>
              </div>
              <div className="flex w-full flex-col items-start gap-4">
                <div className="flex w-full items-center gap-4">
                  <IconWithBackground
                    variant="neutral"
                    size="small"
                    icon={<DollarSign />}
                  />
                  <div className="flex grow shrink-0 basis-0 flex-col items-start">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Pay with
                    </span>
                    <span className="text-body font-body text-subtext-color">
                      USD Wallet
                    </span>
                  </div>
                  <ChevronRight className="text-heading-3 font-heading-3 text-default-font" />
                </div>
                <div className="flex w-full flex-col items-start gap-2 pl-3">
                  <div className="flex h-4 w-px flex-none flex-col items-center gap-2 bg-neutral-border" />
                </div>
                <div className="flex w-full items-center gap-4">
                  <IconWithBackground
                    variant="neutral"
                    size="small"
                    icon={<DollarSign />}
                  />
                  <div className="flex grow shrink-0 basis-0 flex-col items-start">
                    <span className="text-body-bold font-body-bold text-default-font">
                      Buy
                    </span>
                    <span className="text-body font-body text-subtext-color">
                      Bitcoin
                    </span>
                  </div>
                  <ChevronRight className="text-heading-3 font-heading-3 text-default-font" />
                </div>
              </div>
              <Alert>
                <AlertDescription>
                  <strong>Market order</strong> - You're placing a market order which will execute immediately.
                </AlertDescription>
              </Alert>
              <Button
                className="h-10 w-full flex-none"
                size="lg"
                onClick={(event: React.MouseEvent<HTMLButtonElement>) => {}}
              >
                Preview buy
              </Button>
            </div>
            <div className="flex w-full flex-col items-start gap-4 border-b border-solid border-neutral-border px-6 py-6">
              <div className="flex w-full items-center gap-4">
                <IconWithBackground size="small" icon={<Send />} />
                <span className="grow shrink-0 basis-0 text-body-bold font-body-bold text-default-font">
                  Send crypto
                </span>
              </div>
              <div className="flex w-full items-center gap-4">
                <IconWithBackground size="small" icon={<Download />} />
                <span className="grow shrink-0 basis-0 text-body-bold font-body-bold text-default-font">
                  Receive crypto
                </span>
              </div>
              <div className="flex w-full items-center gap-4">
                <IconWithBackground size="small" icon={<DollarSign />} />
                <span className="grow shrink-0 basis-0 text-body-bold font-body-bold text-default-font">
                  Deposit cash
                </span>
              </div>
              <div className="flex w-full items-center gap-4">
                <IconWithBackground size="small" icon={<CreditCard />} />
                <span className="grow shrink-0 basis-0 text-body-bold font-body-bold text-default-font">
                  Withdraw cash
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DefaultPageLayout>
  );
}

export default CryptoDashboard;
